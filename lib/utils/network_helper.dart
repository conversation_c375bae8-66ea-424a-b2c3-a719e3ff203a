import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

class NetworkHelper {
  static final NetworkHelper _instance = NetworkHelper._internal();
  factory NetworkHelper() => _instance;
  NetworkHelper._internal();

  /// 检查网络连接状态
  static Future<bool> isConnected() async {
    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      if (connectivityResult == ConnectivityResult.none) {
        debugPrint('网络连接检查: 无网络连接');
        return false;
      }
      
      // 进一步检查实际的网络连通性
      return await hasInternetConnection();
    } catch (e) {
      debugPrint('网络连接检查失败: $e');
      return false;
    }
  }

  /// 检查实际的互联网连接
  static Future<bool> hasInternetConnection() async {
    try {
      // 尝试连接到多个可靠的服务器
      final List<String> testHosts = [
        'google.com',
        'baidu.com',
        '*******', // Google DNS
        '***************', // 国内 DNS
      ];

      for (String host in testHosts) {
        try {
          final result = await InternetAddress.lookup(host);
          if (result.isNotEmpty && result[0].rawAddress.isNotEmpty) {
            debugPrint('网络连通性检查: 通过 $host 连接成功');
            return true;
          }
        } catch (e) {
          debugPrint('连接 $host 失败: $e');
          continue;
        }
      }
      
      debugPrint('网络连通性检查: 所有测试主机都无法连接');
      return false;
    } catch (e) {
      debugPrint('网络连通性检查失败: $e');
      return false;
    }
  }

  /// 测试 Supabase 连接
  static Future<bool> testSupabaseConnection() async {
    try {
      debugPrint('测试 Supabase 连接...');
      
      // 尝试解析 Supabase 域名
      final result = await InternetAddress.lookup('vqeqhvwrfdvpuocgnrxt.supabase.co');
      if (result.isNotEmpty) {
        debugPrint('Supabase 域名解析成功: ${result.first.address}');
        return true;
      } else {
        debugPrint('Supabase 域名解析失败: 无结果');
        return false;
      }
    } catch (e) {
      debugPrint('Supabase 连接测试失败: $e');
      return false;
    }
  }

  /// 获取网络诊断信息
  static Future<Map<String, dynamic>> getDiagnosticInfo() async {
    final Map<String, dynamic> info = {};
    
    try {
      // 连接类型
      final connectivityResult = await Connectivity().checkConnectivity();
      info['connectivity'] = connectivityResult.toString();
      
      // 基本网络连通性
      info['hasInternet'] = await hasInternetConnection();
      
      // Supabase 连接测试
      info['supabaseReachable'] = await testSupabaseConnection();
      
      // DNS 测试
      info['dnsWorking'] = await _testDNS();
      
    } catch (e) {
      info['error'] = e.toString();
    }
    
    return info;
  }

  static Future<bool> _testDNS() async {
    try {
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// 打印网络诊断信息
  static Future<void> printDiagnostics() async {
    debugPrint('=== 网络诊断信息 ===');
    final info = await getDiagnosticInfo();
    info.forEach((key, value) {
      debugPrint('$key: $value');
    });
    debugPrint('==================');
  }
}
