#!/bin/bash

echo "🚀 开始快速测试修复效果..."

# 1. 清理项目
echo "📦 清理项目..."
flutter clean

# 2. 获取依赖
echo "📥 获取依赖..."
flutter pub get

# 3. 分析代码
echo "🔍 分析代码..."
flutter analyze

# 4. 检查网络连接
echo "🌐 检查网络连接..."
ping -c 3 google.com
ping -c 3 vqeqhvwrfdvpuocgnrxt.supabase.co

# 5. 构建应用
echo "🔨 构建应用..."
flutter build apk --debug

echo "✅ 测试完成！"
echo ""
echo "📋 下一步操作："
echo "1. 运行 'flutter run' 启动应用"
echo "2. 查看控制台的网络诊断信息"
echo "3. 测试登录功能"
echo "4. 如果仍有网络问题，请参考 NETWORK_TROUBLESHOOTING.md"
